import { OptionalTextType, Overwrite, Prettify, TextType } from 'adpod-tools';
import { Vmap, VMAP, VmapAdBreak, VmapAdBreakNormalized } from './vmap.type';

export type PrerollVmap = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAP }>;

export type PrerollVMAP = Prettify<
  Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreak }> & PrerollVMAPDebug
>;

type PrerollVMAPDebug = {
  Debug?: {
    RAWPrerollRequestURL: OptionalTextType;
    PrerollRequestURL: TextType;
    ValidationResult: TextType;
    WhatsOnStatus: TextType;
  };
};

// PREROLL NORMALIZED

export type PrerollVmapNormalized = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAPNormalized }>;

export type PrerollVMAPNormalized = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreakNormalized }>;
