import dayjs from 'dayjs';
import {
  IConfiguration,
  xmlParser,
  Channel,
  formatDayJsDateToHuman,
  LogLevel,
  Vast4Normalized
} from 'adpod-tools';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { Injectable } from '@nestjs/common';
import { createEmptyVast } from '../../scripts/vast/createEmptyVast';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PlaylistMode } from '../../models/playlistMode.model';
import { PrefetchType } from '../../models/prefetch.model';
import { injectReqParams } from '../../scripts/configuration/injectReqParams/injectReqParams';
import getPlaylistModeByPrefetchMode from '../../scripts/vast/getPlaylistModeByPrefetchMode';
import getPrefetchTimeByPrefetchMode, {
  PrefetchTime
} from '../../scripts/vast/getPrefetchTimeByPrefetchMode';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { generatePlaylistBreakInfo } from '../../scripts/logs/generatePlaylistBreakInfo';
import { isPrefetchNext } from '../../scripts/playlist/isPrefetchNext';
import { isPlaylistModeDefault } from '../../scripts/playlist/isPlaylistModeDefault';
import {
  RequestHeaders,
  IPlaylistInfoItem,
  IPlaylistResponse,
  IRequestLog
} from '../../interfaces';
import logger from '../../libs/logging/logger';
import { validators } from '../../EnvValidation/envalidConfig';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { ConfigWithPlaylist } from '../../interfaces/configWithPlaylist';
import { IConfigurationService } from './services/configuration.service';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import { WorkerConfigCacheService } from '../../libs';
import { dropUnnecessaryAttributesFromVast4 } from '../../scripts/utils/dropAttributes';

dayjs.extend(isSameOrAfter);

@Injectable()
export class PlaylistMultipleService {
  constructor(
    private readonly jsonPlaylist: JsonPlaylistService,
    private readonly playlistMerger: PlaylistMerger,
    private readonly configurationService: IConfigurationService,
    private readonly generalPlaylistTransformer: GeneralPlaylistTransformer,
    private readonly workerConfigCacheService: WorkerConfigCacheService
  ) {}

  public async createMultipleBreaksVastPlaylist(
    mode: PrefetchType,
    version: string,
    requestMacroParams: RequestMacroParams,
    allowAnyStartDate: boolean,
    startDate?: string,
    custParams?: string,
    startBid?: string,
    headers: RequestHeaders = {},
    output = PlaylistOutputs.default,
    channel = Channel.ttv,
    duration?: string,
    ip?: string,
    ua?: string | string[]
  ): Promise<IPlaylistResponse> {
    const dateNow = dayjs();
    const startFrom = startDate
      ? dayjs(startDate.replace(' ', '+'))
      : startBid
        ? dateNow
        : dateNow.add(validators.PREFETCH_OFFSET, 'seconds');

    const versions = version.split(',');
    const isPrefetchNextMode = isPrefetchNext(mode);

    const requestLog = {
      channel,
      startBid,
      configVersion: version,
      mode,
      startFrom: formatDayJsDateToHuman(startFrom),
      output,
      requestMacroParams
    };

    logger('PREFETCH_PLAYLIST_START', requestLog, LogLevel.dev);

    const { next } = await this.workerConfigCacheService.getScheduleConfigsAvailability();
    const prefetchTime = getPrefetchTimeByPrefetchMode(mode, next);

    const versionPromises = versions.map(async (v) =>
      this.processConfigurationPerVersion(
        startFrom,
        channel,
        v,
        allowAnyStartDate,
        requestLog,
        mode,
        output,
        isPrefetchNextMode,
        requestMacroParams,
        headers,
        prefetchTime,
        custParams,
        startBid,
        duration,
        ip,
        ua
      )
    );
    const configsWithDaiAds = await Promise.all(versionPromises);

    if (configsWithDaiAds.every((x) => x.emptyVastReason)) {
      return await this.returnEmptyVastResponse(
        configsWithDaiAds[0].emptyVastReason!,
        requestLog,
        output
      );
    }

    const finalPlaylist = this.playlistMerger.mergeMultiple(
      configsWithDaiAds.map((x) => x.configsWithPlaylists)
    );

    if (!finalPlaylist.length) {
      return await this.returnEmptyVastResponse('no playlist after merge', requestLog, output);
    }

    return this.preparePlaylistResponse(
      finalPlaylist,
      mode,
      prefetchTime.prefetchTime,
      channel,
      version,
      output,
      requestLog
    );
  }

  private async processConfigurationPerVersion(
    startFrom: dayjs.Dayjs,
    channel: Channel,
    version: string,
    allowAnyStartDate: boolean,
    requestLog: any,
    mode: PrefetchType,
    output: PlaylistOutputs,
    isPrefetchNextMode: boolean,
    requestMacroParams: RequestMacroParams,
    headers: RequestHeaders,
    { minutes }: PrefetchTime,
    custParams?: string,
    startBid?: string,
    duration?: string,
    ip?: string,
    ua?: string | string[]
  ): Promise<{ emptyVastReason: string | null; configsWithPlaylists: ConfigWithPlaylist[] }> {
    const foundConfigsSorted = await this.configurationService.getConfigurations(
      startFrom,
      channel,
      version,
      minutes!,
      allowAnyStartDate,
      isPrefetchNextMode,
      startBid
    );

    // validate
    if (!foundConfigsSorted.length) {
      logger('PREFETCH_PLAYLIST_NO_CONFIGS', requestLog, LogLevel.dev);
      const emptyVastReason = 'no configs';
      return {
        emptyVastReason,
        configsWithPlaylists: []
      };
    }

    logger(
      'PREFETCH_PLAYLIST_FOUND_CONFIGS',
      {
        ...requestLog,
        amount: foundConfigsSorted.length,
        breaks: foundConfigsSorted.map((c) => ({ time: c.time, id: c.id }))
      },
      LogLevel.dev
    );

    logger(
      'PREFETCH_PLAYLIST_CONFIGS',
      {
        bids: foundConfigsSorted.map((c) => c.id),
        ...requestLog
      },
      LogLevel.dev
    );

    // validate configs
    if (foundConfigsSorted.length && duration && isPrefetchNextMode) {
      const breakDuration = foundConfigsSorted[0].duration;

      logger(
        'BREAK_DURATION_VALIDATION',
        { expectedDuration: duration, breakDuration },
        LogLevel.dev
      );

      if (parseInt(duration) !== breakDuration) {
        const emptyVastReason = 'break duration different than expected';
        return {
          emptyVastReason,
          configsWithPlaylists: []
        };
      }
    }

    if (!foundConfigsSorted.length) {
      const emptyVastReason = 'no configs by time range';
      return {
        emptyVastReason,
        configsWithPlaylists: []
      };
    }

    // create XML playlist
    const configsWithPlaylists = await this.createBreaksPlaylist(
      foundConfigsSorted,
      mode,
      requestMacroParams,
      channel,
      version,
      output,
      headers,
      custParams,
      ip,
      ua
    );

    logger('PREFETCH_PLAYLIST_FINISH', {}, LogLevel.dev);

    return {
      emptyVastReason: null,
      configsWithPlaylists
    };
  }

  private async createBreaksPlaylist(
    configs: IConfiguration[],
    prefetchModeType: PrefetchType,
    requestMacroParams: RequestMacroParams,
    channel: Channel,
    version: string,
    output: PlaylistOutputs,
    headers: RequestHeaders = {},
    custParams?: string,
    ip?: string,
    ua?: string | string[]
  ): Promise<ConfigWithPlaylist[]> {
    const configsWithPlaylist: ConfigWithPlaylist[] = [];
    const mode = getPlaylistModeByPrefetchMode(prefetchModeType);

    for await (let config of configs) {
      config = injectReqParams(config, requestMacroParams, headers, ip, ua);

      const breakDetails = await this.jsonPlaylist.create(
        config,
        mode,
        channel,
        version,
        requestMacroParams,
        custParams,
        output,
        headers,
        ip,
        ua
      );

      configsWithPlaylist.push({
        config,
        breakDetails
      });
    }

    return configsWithPlaylist;
  }

  private async preparePlaylistResponse(
    playlists: ConfigWithPlaylist[],
    mode: PrefetchType,
    prefetchTime: string | undefined,
    channel: Channel,
    version: string,
    output: PlaylistOutputs,
    requestLog: IRequestLog
  ): Promise<IPlaylistResponse> {
    let vasts: Vast4Normalized[] = [];
    let filteredConfigs: IConfiguration[] = [];
    let hasReplacedAds = false;
    const playlistInfo: (IPlaylistInfoItem | null)[] = [];

    const playlistMode = getPlaylistModeByPrefetchMode(mode);
    const hasDefaultPlaylistMode = isPlaylistModeDefault(playlistMode);

    const isNextReplaced = [
      PrefetchType.nextReplaced,
      PrefetchType.nextReplacedDebug
    ].includes(mode);

    for await (const playlist of playlists) {
      const { vast4Json, isWithReplacedAds, adServerResponseLog } = playlist.breakDetails;

      playlistInfo.push(
        generatePlaylistBreakInfo(vast4Json, version, channel, adServerResponseLog)
      );

      if (isWithReplacedAds && !hasReplacedAds) {
        hasReplacedAds = true;
      }

      if (isNextReplaced ? isWithReplacedAds : isWithReplacedAds || !hasDefaultPlaylistMode) {
        filteredConfigs.push(playlist.config);
        vasts.push(vast4Json);
      }
    }

    if (isNextReplaced && vasts.length && playlists.length) {
      filteredConfigs = [filteredConfigs[0]];
      vasts = [vasts[0]];
    }

    const hasEmptyVastState = !hasReplacedAds && (hasDefaultPlaylistMode || isNextReplaced);

    if (hasEmptyVastState) {
      const emptyVastReason = 'no replaced ads';
      return await this.returnEmptyVastResponse(emptyVastReason, {} as IRequestLog, output);
    }

    vasts = vasts.map(dropUnnecessaryAttributesFromVast4);

    const playlistXml = xmlParser.fromJSONtoXML(
      output === PlaylistOutputs.default
        ? this.generalPlaylistTransformer.injectPrefetchTimeToVmap(
            await this.generalPlaylistTransformer.convertToVmap(
              vasts,
              filteredConfigs,
              playlistMode
            ),
            prefetchTime
          )
        : this.generalPlaylistTransformer.convertToVAST4(vasts, prefetchTime),
      playlistMode === PlaylistMode.debug
    );

    return {
      playlist: this.generalPlaylistTransformer.replaceNotAllowedCharsInXml(playlistXml, mode),
      emptyVastReason: null,
      playlistInfo,
      requestLog
    };
  }

  private async returnEmptyVastResponse(
    emptyVastReason: string,
    requestLog: IRequestLog,
    output: PlaylistOutputs
  ) {
    return {
      playlist: await createEmptyVast(requestLog, emptyVastReason, output),
      emptyVastReason,
      playlistInfo: null,
      requestLog
    };
  }
}
