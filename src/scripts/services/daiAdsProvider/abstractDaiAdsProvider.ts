import { AdVast4Normalized, ExtensionWB<PERSON>MP, logger, LogLevel, request } from 'adpod-tools';
import { validators } from '../../../EnvValidation/envalidConfig';
import {
  logRequestStatsInput,
  AdserverAdPreParse,
  DaiAdsProviderOutputCommon,
  fetchAdsOutput,
  AdserverAd,
  DaiAdsProviderOutput,
  DaiAdsProviderOutputBreak
} from '../../../interfaces';
import { EnhancedVastCountryEnum } from '../../../models/enhancedVastCountry.model';
import { URLParamsHelper } from '../../adserver/urlHelper';

export abstract class DaiAdsProvider {
  protected abstract logAdServerRequestStats(args: logRequestStatsInput): void;

  protected abstract requestAd(...args: any): Promise<AdserverAdPreParse>;

  protected abstract fetchAds(inputArgs: DaiAdsProviderOutputCommon): Promise<fetchAdsOutput>;

  public abstract provideAdCoordinator(
    inputArgs: DaiAdsProviderOutputCommon
  ): Promise<AdserverAd[]>;

  protected abstract parseResponse(jsonVast: object | null): Promise<AdVast4Normalized[]>;

  private createMediaEnhancedVastUrl(
    universalAdId: string,
    enhancedVastCountry: EnhancedVastCountryEnum
  ) {
    let mediaEnhancedURL = validators.IT_MEDIA_ENHANCED_URL.replaceAll(
      '$UNIVERSAL_AD_ID',
      universalAdId
    );

    if (enhancedVastCountry === EnhancedVastCountryEnum.pl) {
      mediaEnhancedURL = validators.PL_MEDIA_ENHANCED_URL.replaceAll(
        '$UNIVERSAL_AD_ID',
        universalAdId
      );
    }

    logger('MEDIA_ENHANCED_URL', { mediaEnhancedURL }, LogLevel.debug);

    return mediaEnhancedURL;
  }

  protected handleGdprsForAdServerUrl(
    adserverUrl: string,
    gdpr: { value?: string; name: string },
    gdprConsent: { value?: string; name: string },
    npa?: string,
    separator = '&'
  ) {
    const helper = new URLParamsHelper(adserverUrl, separator);
    const { value: gdprValue, name: gdprName } = gdpr;
    const { value: gdprConsentValue, name: gdprConsentName } = gdprConsent;

    if (!gdprValue) {
      helper.delete(gdprName);
      helper.delete(gdprConsentName);
    }

    if (gdprValue === '0') {
      helper.delete(gdprConsentName);
    }

    if (!!npa) {
      helper.delete(gdprConsentName);
      helper.delete(gdprName);
      helper.add('npa', npa);
    }

    if (!npa && !gdprValue && !gdprConsentValue) {
      helper.add('npa', '0');
    }

    return helper.toString();
  }

  protected async fetchMediaEnhancedVast(
    universalAdId: string,
    enhancedVastCountry: EnhancedVastCountryEnum
  ): Promise<string | null> {
    const mediaEnhancedVastUrl = this.createMediaEnhancedVastUrl(
      universalAdId,
      enhancedVastCountry
    );

    try {
      const resData = await request(mediaEnhancedVastUrl);
      const textData = await resData.text();

      return textData;
    } catch (err) {
      logger(
        'ERROR_FETCH_MEDIA_ENHANCED_VAST',
        { err, url: mediaEnhancedVastUrl },
        LogLevel.dev
      );
    }

    return null;
  }

  private makeWBDAPMExtension(breakId: string, slotPosition: number): ExtensionWBDAMP {
    return {
      _attributes: { type: 'wbdapm' },
      SlotParameters: {
        _cdata: `{ "breakID": ${breakId}, "breakType": "mirrored", "slotPosition": ${slotPosition}, "slotType": "mirrored" }`
      }
    };
  }

  protected addWBDAPMExtensionToAd(adData: AdVast4Normalized[]): AdVast4Normalized[] {
    const updatedAdData = adData;
    for (const ad of updatedAdData) {
      if (!ad.InLine) {
        continue;
      }

      const currentExtensions = ad.InLine.Extensions.Extension;
      const wbdApmExtension = this.makeWBDAPMExtension(
        ad.attributesToDrop?.breakId || '',
        ad._attributes.sequence
      );

      ad.InLine.Extensions.Extension = [wbdApmExtension, ...currentExtensions];
    }
    return updatedAdData;
  }

  protected addWBDAPMExtentionsToAdData(adData: AdserverAd[]): AdserverAd[] {
    return adData.map((ad) => {
      ad.vast = this.addWBDAPMExtensionToAd(ad.vast);
      return ad;
    });
  }
}

export abstract class DaiAdsProvider_REST extends DaiAdsProvider {
  protected abstract extractDataFromDaiAds(
    daiAds: AdserverAdPreParse[],
    inputArgs: DaiAdsProviderOutput
  ): Promise<AdserverAd[]>;
}

export abstract class DaiAdsProvider_ADO_BREAK extends DaiAdsProvider {
  protected abstract extractDataFromDaiAds(
    daiAds: AdserverAdPreParse[],
    inputArgs: DaiAdsProviderOutputBreak
  ): Promise<AdserverAd[]>;
}
