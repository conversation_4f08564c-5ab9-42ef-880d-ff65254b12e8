import { Injectable } from '@nestjs/common';
import {
  xmlParser,
  LogLevel,
  request,
  Vast4,
  RegistryId,
  normalizeVast4,
  AdVast4Normalized,
  BreakConnector,
  hmsToMilliseconds,
  MediaFile,
  logger,
  getMediaFileType
} from 'adpod-tools';
import {
  AdserverAd,
  Vmap,
  RequestHeaders,
  TrackingScriptsEnum,
  VmapAdBreak,
  slotFWTrackingScriptsType,
  DaiAdsProviderOutput,
  AdserverAdPreParse,
  AdserverAdFreeWheel,
  logRequestStatsInputATV
} from '../../../../interfaces';
import {
  getCurrentPerformanceTime,
  formatPerformanceTime
} from '../../../utils/performanceTime';
import { createFWRequestUrl } from '../../../adserver/createFWRequestUrl';
import { DaiAdsProvider_REST } from '../abstractDaiAdsProvider';
import { validators } from '../../../../EnvValidation/envalidConfig';
import { WorkerConfigType } from '../../../../models/workerConfig';
import { EnhancedVastCountryEnum } from '../../../../models/enhancedVastCountry.model';
import { IDebugService } from '../../../../libs/caching/services/debug.service';
import { isEmptyVast } from '../../../vast/isEmptyVast';
import { ICacheProvider } from '../../../../libs/caching';

@Injectable()
export class FreeWheelDaiAdsProvider extends DaiAdsProvider_REST {
  constructor(
    private readonly localCache: ICacheProvider,
    private readonly debugService: IDebugService
  ) {
    super();
  }

  protected logAdServerRequestStats({
    startFetchTime,
    endFetchTime,
    requestedSlots,
    returnedSlots,
    filledSlots,
    version,
    connector,
    channel,
    isProgrammatic
  }: logRequestStatsInputATV): void {
    const processingTime = +formatPerformanceTime(startFetchTime, endFetchTime);

    const processingTimeRounded = Math.round(processingTime / 10);

    logger(
      `STATS_ADSERVER_FW_PTR_${processingTimeRounded}_REQUSTEDSLOTS_${requestedSlots}_RETURNEDSLOTS_${returnedSlots}_FILLEDSLOTS_${filledSlots}_V_${version}_CONNECTOR_${connector}_CHANNEL_${channel}`,
      {
        requestedSlots,
        returnedSlots,
        filledSlots,
        processingTime,
        processingTimeRounded,
        version,
        connector,
        channel,
        isProgrammatic
      },
      +processingTime > validators.LOG_ADSERVER_RESPONSE_PROCESSING_TIME_WARN_THRESHOLD
        ? LogLevel.warn
        : LogLevel.statsAdserverFw
    );
  }

  protected async requestAd({
    breakId,
    adrequest,
    connector,
    gdpr,
    gdprConsent,
    position,
    custParams,
    ua: userAgent,
    version,
    npa
  }: {
    ip?: string;
    ua?: string | string[];
    headers: RequestHeaders;
    breakId: string;
    adrequest: string;
    connector: BreakConnector;
    position: number;
    duration: number;
    version: string;
    gdpr?: string;
    gdprConsent?: string;
    custParams?: string;
    npa?: string;
  }): Promise<AdserverAdPreParse> {
    const adServerUrl = this.handleGdprsForAdServerUrl(
      createFWRequestUrl(adrequest, gdpr, custParams),
      { value: gdpr, name: '_fw_gdpr' },
      { value: gdprConsent, name: '_fw_gdpr_consent' },
      npa
    );

    const loggerData = {
      breakId,
      initialAdRequest: adrequest,
      finalAdRequest: adServerUrl,
      connector,
      gdpr,
      gdprConsent,
      position,
      custParams,
      userAgent
    };

    logger('FW_FETCH_AD', loggerData, LogLevel.freewheel);

    const response: AdserverAdPreParse = {
      vast: null,
      position,
      adServerUrl,
      breakId,
      connector,
      isReplaced: false
    };

    try {
      const resData = await request(adServerUrl, {
        headers: {
          'user-agent': userAgent,
          'x-device-user-agent': userAgent
        }
      });

      const xmlVast = await resData.text();

      response.vast = xmlVast;
      response.isReplaced = !isEmptyVast(xmlVast);
    } catch (err) {
      logger('ERROR_FW_FETCH_AD', { err, response, ...loggerData }, LogLevel.error);
    }

    await this.debugService.setSpotDebugDetails({
      breakId,
      position,
      adServerUrl,
      version,
      connector
    });

    return response;
  }

  protected async fetchAds(inputArgs: DaiAdsProviderOutput): Promise<{
    startFetchTime: number;
    endFetchTime: number;
    daiAds: AdserverAdPreParse[];
  }> {
    const startFetchTime = getCurrentPerformanceTime();

    const { headers, gdpr, gdprConsent, custParams, ip, ua, npa } = inputArgs.request;
    const { breakId, connector, atvSpots, version } = inputArgs.config;

    const allResponses: AdserverAdPreParse[] = await Promise.all(
      atvSpots.map(({ adrequest, position, duration }) => {
        return this.requestAd({
          ip,
          ua,
          headers,
          breakId,
          adrequest,
          connector,
          gdpr,
          gdprConsent,
          position,
          duration,
          version,
          custParams,
          npa
        });
      })
    ).catch((error) => {
      logger('ERROR_FW_HANDLING_ADSERVER_VASTS', { error });
      return [];
    });

    const endFetchTime = getCurrentPerformanceTime();

    const daiAds = allResponses.filter(({ vast }) => !!vast);

    return { startFetchTime, endFetchTime, daiAds };
  }

  protected getBreakTrackingScripts(
    jsonVast: Vmap
  ): { slotImpressionUrl: string; slotEndUrl: string } | undefined {
    const vmapTracking = (jsonVast['vmap:VMAP']?.['vmap:AdBreak'] as VmapAdBreak)?.[
      'vmap:TrackingEvents'
    ]?.['vmap:Tracking'];

    return vmapTracking
      ? {
          slotImpressionUrl:
            vmapTracking.find((t) => t._attributes.event === 'breakStart')?._cdata.trim() ??
            '',
          slotEndUrl:
            vmapTracking.find((t) => t._attributes.event === 'breakEnd')?._cdata.trim() ?? ''
        }
      : undefined;
  }

  private handleAdDataTag(jsonVast: Vmap | null): AdVast4Normalized[] {
    const vmapVASTAdData = (jsonVast?.['vmap:VMAP']?.['vmap:AdBreak'] as VmapAdBreak)?.[
      'vmap:AdSource'
    ]?.['vmap:VASTAdData'];

    if (!jsonVast || !vmapVASTAdData) {
      logger('FW_VAST_AD_DATA_OR_VMAP_EMPTY', undefined, LogLevel.warn);
      return [];
    }
    const vast4Normalized = normalizeVast4(vmapVASTAdData);

    return vast4Normalized.VAST.Ad;
  }

  private isProgrammaticTypeAd(adVast: AdVast4Normalized[]): boolean[] {
    const isProgrammatic = adVast.map((ad) => {
      if (!ad.InLine) {
        return false;
      }

      return ad
        .InLine!.AdTitle._text.toLowerCase()
        .includes(validators.FW_AD_PROGRAMMATIC_INDICATOR);
    });

    logger('FW_RESPONSE_PROGRAMMATIC_STATUS', { isProgrammatic }, LogLevel.freewheel);

    return isProgrammatic;
  }

  private getdaiAdsDuration(adVast: AdVast4Normalized[]): number {
    return adVast.reduce((acc, curr) => {
      const adDurationHms = curr.InLine?.Creatives.Creative[0].Linear?.Duration._text;
      if (!adDurationHms) {
        return acc;
      }

      const msDuration = hmsToMilliseconds(adDurationHms);
      const duration = msDuration ? msDuration / 1000 : null;

      if (!duration) {
        return acc;
      }
      acc += duration;

      return acc;
    }, 0);
  }

  private getUniversalAdIdValue(adVast: AdVast4Normalized[]): string[] | null {
    return adVast.length
      ? adVast.map((vast) => vast.InLine?.Creatives.Creative[0].UniversalAdId?._text ?? 'n/a')
      : null;
  }

  private getMezzanineAdId(adVast: AdVast4Normalized[]): (string | null)[] {
    return adVast.map((el) => {
      const creativesTag = el.InLine?.Creatives;
      if (!creativesTag) {
        logger('FW_RESPONSE_WITHOUT_CREATIVES_TAG', { vast: el }, LogLevel.warn);
        return null;
      }

      return (
        creativesTag.Creative[0]
          .Linear!.MediaFiles.MediaFile[0]._cdata?.split('/')
          .slice(-1)[0]
          .trim() ?? null
      );
    });
  }

  private getSlotFWTrackingScripts(adVast: AdVast4Normalized[]): slotFWTrackingScriptsType[] {
    const events: slotFWTrackingScriptsType[] | null = [];

    for (const [singleAdVastIndex, singleAdVastValue] of Object.entries(adVast)) {
      const verificationParamsString =
        singleAdVastValue.InLine?.AdVerifications?.Verification?.VerificationParameters
          ?._cdata;

      if (verificationParamsString) {
        try {
          const verificationParamsStringJSON = JSON.parse(verificationParamsString);
          const trackingAndImpressionEvents =
            verificationParamsStringJSON.tracking_configuration?.tracking_events;

          if (!trackingAndImpressionEvents) {
            return events;
          }

          const acceptedTrackingEvents = [
            'start',
            'firstquartile',
            'midpoint',
            'thirdquartile',
            'complete'
          ];

          const acceptedImpressionEvents = ['measurable_impression', 'viewable_impression'];

          for (const name in trackingAndImpressionEvents) {
            const [value] = trackingAndImpressionEvents[name];

            const type = acceptedTrackingEvents.includes(name)
              ? TrackingScriptsEnum.tracking
              : acceptedImpressionEvents.includes(name)
                ? TrackingScriptsEnum.impression
                : null;

            if (type) {
              events.push({ adIndex: +singleAdVastIndex, name, value, type });
            }
          }
        } catch (error: unknown) {
          logger(
            'ERROR_FW_RESPONSE_PROGRAMMATIC_GET_SLOT_TRACKINGS_EVENTS',
            { error, verificationParamsString },
            LogLevel.error
          );
          return events;
        }
      }
    }

    logger(
      'FW_RESPONSE_PROGRAMMATIC_GET_SLOT_TRACKINGS_EVENTS',
      { events },
      LogLevel.freewheel
    );

    return events;
  }

  private replaceDaiAdsCreativeId(daiAds: AdserverAdFreeWheel[]): AdserverAdFreeWheel[] {
    return daiAds.map((el) => {
      el.vast = el.vast.map((vastEl, vastElIndex) => {
        const creativeAttributes = vastEl?.InLine?.Creatives?.Creative[0]?._attributes;
        const adUniversalAdId = el.universalAdId?.[vastElIndex];
        const mezzanineAdId = el.mezzanineAdId?.[vastElIndex];

        if (creativeAttributes && adUniversalAdId) {
          if (adUniversalAdId !== 'n/a') {
            creativeAttributes.id = adUniversalAdId;
          } else if (mezzanineAdId) {
            creativeAttributes.id = mezzanineAdId;
          }
        }

        return vastEl;
      });

      return el;
    });
  }

  private replaceUniversalAdId(daiAds: AdserverAdFreeWheel[]): AdserverAdFreeWheel[] {
    return daiAds.map((el) => {
      el.vast = el.vast.map((vastEl, vastElIndex) => {
        const creativeTag = vastEl?.InLine?.Creatives?.Creative[0];

        if (!creativeTag) {
          return vastEl;
        }

        creativeTag.UniversalAdId = {
          _text:
            (el.isProgrammatic?.[vastElIndex]
              ? el.universalAdId?.[vastElIndex]
                ? el.universalAdId[vastElIndex]
                : 'n/a'
              : el.mezzanineAdId[vastElIndex]) ?? 'n/a',
          _attributes: { idRegistry: RegistryId.di }
        };

        return vastEl;
      });

      return el;
    });
  }

  private extractFilenameFromUrl(url: string): string {
    const parts = url.split('/');
    const lastPart = parts.pop();
    return lastPart && lastPart.length > 0 ? lastPart : '';
  }

  private isStrictWbdFilename(input: string): boolean {
    const pattern = /^wbd_\d{2}\.(mp4|flv)$/;
    return pattern.test(input.trim());
  }

  private async replaceProgrammaticDaiAdsMediaFileTag(
    daiAds: AdserverAdFreeWheel[],
    version: string
  ): Promise<AdserverAdFreeWheel[]> {
    const updatedDaiAdsPromises = daiAds.map(async (el) => {
      for (const [elVastIndex] of Object.entries(el.vast)) {
        if (el.isProgrammatic?.[elVastIndex]) {
          const linearTag = (el.vast?.[elVastIndex] as AdVast4Normalized)?.InLine?.Creatives
            ?.Creative[0]?.Linear;
          const adVastUniversalAdId = el.universalAdId?.[elVastIndex];

          if (linearTag && adVastUniversalAdId && adVastUniversalAdId !== 'n/a') {
            const workerConfig = (await this.localCache.get(
              'workerConfig'
            )) as WorkerConfigType;
            const enchancedVastPlVersions = workerConfig?.programmaticEnchancedVastPlversions;

            const enhancedVastCountry = enchancedVastPlVersions.includes(version)
              ? EnhancedVastCountryEnum.pl
              : EnhancedVastCountryEnum.it;

            const enhancedVast = await this.fetchMediaEnhancedVast(
              adVastUniversalAdId,
              enhancedVastCountry
            );

            if (enhancedVast) {
              const enhancedVastJson: Vast4 = xmlParser.fromXMLtoJSON(enhancedVast) as Vast4;

              const vast4Normalized = normalizeVast4(enhancedVastJson);

              logger('FW_ENHANCED_VAST_JSON', { json: vast4Normalized }, LogLevel.freewheel);

              const enhancedVastMediaFiles =
                vast4Normalized.VAST.Ad[0]?.InLine?.Creatives?.Creative[0]?.Linear?.MediaFiles;

              const enhancedVastMediaFileArray = enhancedVastMediaFiles?.MediaFile;

              if (enhancedVastMediaFileArray) {
                const allowedMediaFiles: MediaFile[] = [];
                const mezzanineCdata = enhancedVastMediaFiles.Mezzanine?._cdata ?? '';

                for (const enhancedVastEl of enhancedVastMediaFileArray) {
                  if (!enhancedVastEl._cdata?.length) {
                    continue;
                  }

                  if (
                    !this.isStrictWbdFilename(
                      this.extractFilenameFromUrl(enhancedVastEl._cdata)
                    )
                  ) {
                    allowedMediaFiles.push(enhancedVastEl);
                  }
                }

                linearTag.MediaFiles = {
                  MediaFile: allowedMediaFiles,
                  Mezzanine: {
                    _attributes: {
                      delivery: 'progressive',
                      type: getMediaFileType(mezzanineCdata),
                      width: 1920,
                      height: 1080
                    },
                    _cdata: mezzanineCdata.trim()
                  }
                };
              }
            }
          }
        }
      }

      return el;
    });

    return Promise.all(updatedDaiAdsPromises);
  }

  private replaceDefaultDaiAdsMediaFileTag(
    daiAds: AdserverAdFreeWheel[]
  ): AdserverAdFreeWheel[] {
    return daiAds.map((el) => {
      el.vast = el.vast.map((vastEl, vastElIndex) => {
        if (!el.isProgrammatic?.[vastElIndex]) {
          const linearTag = vastEl?.InLine?.Creatives.Creative[0]?.Linear;

          if (!linearTag) {
            return vastEl;
          }

          const firstMediaFile = linearTag.MediaFiles.MediaFile[0];

          if (firstMediaFile) {
            const mezzanineCdata = firstMediaFile?._cdata;
            linearTag.MediaFiles.Mezzanine = {
              _attributes: {
                delivery: 'progressive',
                type: getMediaFileType(mezzanineCdata),
                width: 1920,
                height: 1080
              },
              _cdata: mezzanineCdata
            };
          }
        }
        return vastEl;
      });

      return el;
    });
  }

  private insertTrackingScripts(daiAds: AdserverAdFreeWheel[]): AdserverAdFreeWheel[] {
    return daiAds.map((el) => {
      const { isProgrammatic, slotFWTrackingScripts, vast, breakFWTrackingScripts } = el;
      if (!vast.length || !vast[0].InLine) {
        return el;
      }

      el.vast = el.vast.map((vastEl, vastElIndex) => {
        const inLine = vastEl.InLine;

        if (!inLine) {
          return vastEl;
        }

        if (isProgrammatic?.[vastElIndex] && slotFWTrackingScripts) {
          // Impression
          const additionalImpressionScripts = slotFWTrackingScripts
            .filter(
              ({ type, adIndex }) =>
                type === TrackingScriptsEnum.impression && adIndex === vastElIndex
            )
            .map(({ value }) => ({ _cdata: value, _attributes: { id: '' } }));

          inLine.Impression.push(...additionalImpressionScripts);

          // Tracking
          const additionalTrackingScripts = slotFWTrackingScripts
            .filter(
              ({ type, adIndex }) =>
                type === TrackingScriptsEnum.tracking && adIndex === vastElIndex
            )
            .map(({ value, name }) => ({
              _cdata: value,
              _attributes: { event: name }
            }));

          inLine.Creatives.Creative[0]!.Linear!.TrackingEvents.Tracking.push(
            ...additionalTrackingScripts
          );
        }

        if (breakFWTrackingScripts?.slotImpressionUrl) {
          inLine.Impression.push({
            _attributes: { id: '' },
            _cdata: breakFWTrackingScripts.slotImpressionUrl
          });
        }

        if (breakFWTrackingScripts?.slotEndUrl) {
          inLine.Creatives.Creative[0].Linear!.TrackingEvents.Tracking.push({
            _attributes: { event: 'complete' },
            _cdata: breakFWTrackingScripts.slotEndUrl
          });
        }

        return vastEl;
      });

      return el;
    });
  }

  protected async parseResponse(jsonVmap: object | null): Promise<AdVast4Normalized[]> {
    // logger('FW_FULL_VAST_JSON', { vast: jsonVmap }, LogLevel.freewheel);

    return this.handleAdDataTag(jsonVmap as Vmap | null);
  }

  protected async extractDataFromDaiAds(
    daiAds: AdserverAdPreParse[]
  ): Promise<AdserverAdFreeWheel[]> {
    const daiAdsExtractedData = daiAds.map(async (res) => {
      const jsonVmap: Vmap | null = xmlParser.fromXMLtoJSON(res.vast) as Vmap | null;

      const adData = await this.parseResponse(jsonVmap);

      let isProgrammatic: boolean[] = [];
      let universalAdId: string[] | null = null;
      let mezzanineAdId: (string | null)[] = [];
      let daiAdsDuration: number | null = null;
      let breakFWTrackingScripts:
        | { slotImpressionUrl: string; slotEndUrl: string }
        | undefined;
      let slotFWTrackingScripts: slotFWTrackingScriptsType[] | undefined;

      if (adData.length > 0) {
        breakFWTrackingScripts = this.getBreakTrackingScripts(jsonVmap!);
        mezzanineAdId = this.getMezzanineAdId(adData);
        isProgrammatic = this.isProgrammaticTypeAd(adData);
        daiAdsDuration = this.getdaiAdsDuration(adData);
        universalAdId = this.getUniversalAdIdValue(adData);
        slotFWTrackingScripts = this.getSlotFWTrackingScripts(adData);
      }

      logger(
        'FW_EXTRACTED_DATA',
        {
          isProgrammatic,
          universalAdId,
          mezzanineAdId,
          slotFWTrackingScripts,
          breakFWTrackingScripts,
          daiAdsDuration
        },
        LogLevel.freewheel
      );

      return {
        ...res,
        vast: adData,
        isProgrammatic,
        universalAdId,
        mezzanineAdId,
        slotFWTrackingScripts,
        breakFWTrackingScripts,
        daiAdsDuration
      };
    });

    return Promise.all(daiAdsExtractedData);
  }

  public async provideAdCoordinator(inputArgs: DaiAdsProviderOutput): Promise<AdserverAd[]> {
    const { daiAds, startFetchTime, endFetchTime } = await this.fetchAds(inputArgs);

    let daiAdsExtractedData = await this.extractDataFromDaiAds(daiAds);

    const { atvSpots, version, channel, connector } = inputArgs.config;

    const countFilledSlots = daiAdsExtractedData.filter(({ vast }) => vast.length > 0).length;

    this.logAdServerRequestStats({
      startFetchTime,
      endFetchTime,
      requestedSlots: atvSpots.length,
      returnedSlots: daiAdsExtractedData.length,
      filledSlots: countFilledSlots,
      version,
      connector,
      channel,
      isProgrammatic: daiAdsExtractedData.some(({ isProgrammatic }) => isProgrammatic)
    });

    if (countFilledSlots > 0) {
      const addedSlotTrackings = this.insertTrackingScripts(daiAdsExtractedData);

      const replacedCreativeId = this.replaceDaiAdsCreativeId(addedSlotTrackings);

      const universalAdIdAdded = this.replaceUniversalAdId(replacedCreativeId);

      const replacedProgrammaticDaiAdsMediaFileTag =
        await this.replaceProgrammaticDaiAdsMediaFileTag(universalAdIdAdded, version);

      daiAdsExtractedData = this.replaceDefaultDaiAdsMediaFileTag(
        replacedProgrammaticDaiAdsMediaFileTag
      );
    }

    return daiAdsExtractedData;
  }
}
