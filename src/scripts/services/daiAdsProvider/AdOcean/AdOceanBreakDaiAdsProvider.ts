import { Injectable } from '@nestjs/common';
import {
  xmlParser,
  LogLevel,
  Vast4,
  normalizeVast4,
  AdVast4Normalized,
  Vast4Normalized,
  TransformType,
  ParamsMacro,
  AtvTypeModel,
  BreakConnector,
  logger
} from 'adpod-tools';
import {
  AdserverAd,
  AdserverAdPreParse,
  DaiAdsConfigADOBreak,
  DaiAdsProviderOutputBreak,
  fetchAdsOutput,
  logRequestStatsInputBreak
} from '../../../../interfaces';
import { isEqual } from 'lodash';
import { RequestMacroParams } from '../../../configuration/injectReqParams/requestMacroParams';
import { Protocol } from '../../../../models/protocol.model';
import { URLParamsHelper } from '../../../adserver/urlHelper';
import axios from 'axios';
import { IDebugService } from '../../../../libs/caching/services/debug.service';
import { isEmptyVast } from '../../../vast/isEmptyVast';
import { validators } from '../../../../EnvValidation/envalidConfig';
import { createADORequestUrl } from '../../../adserver/createADORequestUrl';
import {
  getCurrentPerformanceTime,
  formatPerformanceTime
} from '../../../utils/performanceTime';
import { DaiAdsProvider_ADO_BREAK } from '../abstractDaiAdsProvider';

@Injectable()
export class AdOceanBreakDaiAdsProvider extends DaiAdsProvider_ADO_BREAK {
  constructor(private readonly debugService: IDebugService) {
    super();
  }

  public async provideAdCoordinator(
    inputArgs: DaiAdsProviderOutputBreak
  ): Promise<AdserverAd[]> {
    logger(
      'RUN_ADSERVER_BREAK_REQUEST',
      { ids: inputArgs.config.spots.map((s) => s.position) },
      LogLevel.dev
    );
    const { exactLength } = inputArgs.config;

    return exactLength ? this.recursiveRequest(inputArgs) : this.oneRequest(inputArgs);
  }

  private async oneRequest(inputArgs: DaiAdsProviderOutputBreak): Promise<AdserverAd[]> {
    const { daiAds, startFetchTime, endFetchTime } = await this.fetchAds(inputArgs);

    const daiAdsExtractedData = await this.extractDataFromDaiAds(daiAds, inputArgs);

    const adsDataWithExtensions = this.addWBDAPMExtentionsToAdData(daiAdsExtractedData);

    const { version, channel, breakConnector } = inputArgs.config;

    this.logAdServerRequestStats({
      startFetchTime,
      endFetchTime,
      requestedSlots: inputArgs.config.spots.length,
      version,
      connector: breakConnector,
      channel,
      isWithExactLength: inputArgs.config.exactLength,
      finalResponse: adsDataWithExtensions,
      adUnitId: inputArgs.config.adUnitId
    });

    adsDataWithExtensions.map((ad) => {
      return ad.vast.map((ad1) => {
        return ad1.InLine?.Creatives.Creative.map((creative) => {
          if (!creative.Linear?.MediaFiles.Mezzanine) {
            return creative;
          }
          creative.Linear!.MediaFiles.Mezzanine!._attributes.delivery = 'progressive';
          creative.Linear!.MediaFiles.Mezzanine!._attributes.height = 720;
          creative.Linear!.MediaFiles.Mezzanine!._attributes.type = 'video/mp4';
          creative.Linear!.MediaFiles.Mezzanine!._attributes.width = 1280;

          return creative;
        });
      });
    });

    logger('RUN_ADSERVER_BREAK_REQUEST_END', {}, LogLevel.dev);
    return adsDataWithExtensions;
  }

  protected async fetchAds(inputArgs: DaiAdsProviderOutputBreak): Promise<fetchAdsOutput> {
    const startFetchTime = getCurrentPerformanceTime();

    let response: AdserverAdPreParse | null;

    try {
      response = await this.requestAd(inputArgs);
    } catch (error) {
      logger('ERR_HANDLING_ADSERVER_VASTS', { error });
      response = null;
    }

    const endFetchTime = getCurrentPerformanceTime();

    return {
      startFetchTime,
      endFetchTime,
      daiAds: response?.vast ? [response] : []
    };
  }

  protected logAdServerRequestStats(args: logRequestStatsInputBreak): void {
    const {
      startFetchTime,
      endFetchTime,
      requestedSlots,
      version,
      connector,
      channel,
      isWithExactLength,
      finalResponse,
      adUnitId
    } = args;

    const returnedSlots = finalResponse.length;
    const filledSlots = finalResponse.filter(({ vast }) => vast.length > 0).length;
    const processingTime = +formatPerformanceTime(startFetchTime, endFetchTime);

    const isWarn =
      processingTime > validators.LOG_ADSERVER_RESPONSE_PROCESSING_TIME_WARN_THRESHOLD;

    logger(
      `${isWarn ? 'WARN_' : ''}STATS_ADOCEAN_BREAK_SCHEDULE${isWithExactLength ? '_EXACT_LENGTH' : ''}`,
      {
        requestedSlots,
        returnedSlots,
        filledSlots,
        processingTime,
        version,
        connector,
        channel,
        isWithExactLength,
        adUnitId
      },
      isWarn
        ? LogLevel.warn
        : isWithExactLength
          ? LogLevel.statsAdserverAdoBreakExactLength
          : LogLevel.statsAdserverAdoBreak
    );
  }

  async requestAd(inputArgs: DaiAdsProviderOutputBreak): Promise<AdserverAdPreParse> {
    const { breakAdRequest, breakId, version, breakConnector } = inputArgs.config;
    const providerRequest = inputArgs.request;
    const { gdpr, gdprConsent, npa } = providerRequest;

    const adServerUrl = this.handleGdprsForAdServerUrl(
      createADORequestUrl(breakAdRequest, providerRequest),
      { value: gdpr, name: 'gdpr' },
      { value: gdprConsent, name: 'gdpr_consent' },
      npa,
      '/'
    );

    await this.debugService.setBreakDebugDetails({
      breakId,
      adServerUrl,
      version,
      connector: breakConnector
    });

    const response: AdserverAdPreParse = {
      vast: null,
      position: null,
      adServerUrl,
      breakId,
      connector: inputArgs.config.breakConnector ?? BreakConnector.none,
      isReplaced: false
    };

    const timeoutMsg = 'AdOcean request timeout';
    const requestHeaders = {
      'user-agent': providerRequest.ua,
      'x-device-user-agent': providerRequest.ua,
      'x-device-ip': providerRequest.ip,
      'x-forwarded-for': providerRequest.ip,
      'x-device-referrer': providerRequest.headers['x-device-referrer']
    };
    const timeout = validators.AD_OCEAN_REQUEST_TIMEOUT_MS;

    const baseRequestDetails = {
      adServerUrl,
      ip: providerRequest.ip,
      ua: providerRequest.ua,
      requestHeaders,
      timeout,
      breakId
    };

    logger(
      'ADOCEAN_BREAK_SCHEDULE_REQUEST_DETAILS',
      { ...baseRequestDetails },
      LogLevel.adoBreakDebug
    );

    const performanceStart = performance.now();

    try {
      const resData = await axios.get(adServerUrl, {
        headers: requestHeaders,
        timeout,
        timeoutErrorMessage: timeoutMsg,
        responseType: 'text'
      });

      const performanceDone = performance.now();

      const { data, status, statusText, headers: responseHeaders } = resData;
      const responseLength = data?.length;

      logger(
        'ADOCEAN_BREAK_SCHEDULE_RESPONSE_DETAILS',
        {
          performanceMs: +(performanceDone - performanceStart).toFixed(0),
          statusText,
          status,
          responseLength,
          responseContent: responseLength < 3000 ? data : 'n/a',
          ...baseRequestDetails,
          responseHeaders
        },
        LogLevel.adoBreakDebug
      );

      response.vast = data as string;
      response.isReplaced = !isEmptyVast(data);
    } catch (err: unknown) {
      const performanceFail = performance.now();
      const performanceMs = +(performanceFail - performanceStart).toFixed(0);

      if (err instanceof Error && err.message === timeoutMsg) {
        logger(
          'WARN_ADOCEAN_BREAK_SCHEDULE_AXIOS_TIMEOUT',
          {
            performanceMs,
            errorData: err,
            ...baseRequestDetails
          },
          LogLevel.warn
        );
      } else {
        logger(
          'ERROR_ADOCEAN_BREAK_SCHEDULE_REQUEST',
          {
            performanceMs,
            errorData: err,
            ...baseRequestDetails
          },
          LogLevel.error
        );
      }
    }

    return response;
  }

  protected async parseResponse(jsonVast: object | null): Promise<AdVast4Normalized[]> {
    if (!jsonVast) {
      return [];
    }

    const vast4Normalized = normalizeVast4(jsonVast as Vast4);

    return this.getAdoBreakAdsBody(vast4Normalized);
  }

  protected async extractDataFromDaiAds(
    daiAds: AdserverAdPreParse[],
    inputArgs: DaiAdsProviderOutputBreak
  ): Promise<AdserverAd[]> {
    if (daiAds.length !== 1) {
      return [];
    }

    const daiAd = daiAds[0];

    if (isEmptyVast(daiAd.vast)) {
      return [];
    }

    const jsonVast = xmlParser.fromXMLtoJSON(daiAd.vast) as Vast4 | null;

    const adVast4 = await this.parseResponse(jsonVast);

    return adVast4.map(
      (ad, index): AdserverAd => ({
        vast: [ad],
        position: inputArgs.config.spots[index].position,
        adServerUrl: daiAd.adServerUrl,
        breakId: daiAd.breakId,
        connector: daiAd.connector,
        isReplaced: daiAd.isReplaced
      })
    );
  }

  private async recursiveRequest(inputArgs: DaiAdsProviderOutputBreak) {
    const { version, channel, breakConnector, breakAdRequest } = inputArgs.config;
    let finalResponse: AdserverAd[] = [];

    let requestsCounter = 0;

    const fillFinalResponseWithAnotherSlots = async (requestUrl: string) => {
      requestsCounter++;
      const newInputArgs: DaiAdsProviderOutputBreak = {
        ...inputArgs,
        config: {
          ...inputArgs.config,
          breakAdRequest: requestUrl
        }
      };

      const { daiAds } = await this.fetchAds(newInputArgs);

      const daiAdsExtractedData = await this.extractDataFromDaiAds(daiAds, newInputArgs);
      if (daiAdsExtractedData.length === 0) {
        logger('ADO_BREAK_EMPTY_VAST', undefined, LogLevel.dev);
        return;
      }

      const linearConfigAds = this.getConfigLinearAdsIds(newInputArgs.config);

      // current request
      const currentResponseWithoutLinearAds = daiAdsExtractedData.filter(
        (r) =>
          !linearConfigAds.includes(
            r.vast?.[0]?.InLine?.Creatives?.Creative?.[0]._attributes?.id
          )
      );

      const currentResponseSlotsIds = this.getAdsPositionsArr(currentResponseWithoutLinearAds);
      logger(
        'ADO_BREAK_CURRENT_RESPONSE_SLOTS_IDS',
        { currentResponseSlotsIds },
        LogLevel.dev
      );

      const currentResponseAdsIds = this.getAdserverAdsIds(currentResponseWithoutLinearAds);
      logger('ADO_BREAK_CURRENT_RESPONSE_ADS_IDS', { currentResponseAdsIds }, LogLevel.dev);

      // final response
      const finalResponseSlotsIds = this.getAdsPositionsArr(finalResponse);
      logger('ADO_BREAK_FINAL_RESPONSE_SLOTS_IDS', { finalResponseSlotsIds }, LogLevel.dev);

      const finalResponseAdsIds = this.getAdserverAdsIds(finalResponse);
      logger('ADO_BREAK_FINAL_RESPONSE_ADS_IDS', { finalResponseAdsIds }, LogLevel.dev);

      if (!isEqual(currentResponseAdsIds, finalResponseAdsIds)) {
        finalResponse = daiAdsExtractedData;
      }

      if (finalResponse.length === inputArgs.config.spots.length) {
        logger('ADO_BREAK_ALL_SLOTS_REPLACED', undefined, LogLevel.dev);
        return;
      }

      if (!isEqual(finalResponseSlotsIds, currentResponseSlotsIds)) {
        finalResponse = daiAdsExtractedData;

        const notReplacedSlotsConfig = newInputArgs.config.spots.filter(
          (s) => !currentResponseSlotsIds.includes(s.position)
        );

        const notReplacedSlotsRestrictions = notReplacedSlotsConfig
          .flatMap(({ slotRestrictions }) => slotRestrictions)
          .filter((s) => !!s) as string[];

        const nextRequestUrlSlots = newInputArgs.config.spots.filter((c) =>
          currentResponseSlotsIds.includes(c.position)
        );

        const nextRequestUrl = generateADOBreakAdRequest({
          inputArgs: newInputArgs,
          adSlots: nextRequestUrlSlots,
          slotRestrictions: notReplacedSlotsRestrictions,
          onlyPendingSlots: false
        });

        logger(
          'ADO_BREAK_NEXT_REQUEST_URL',
          {
            nextRequestUrlSlots: this.getAdsPositionsArr(nextRequestUrlSlots),
            nextRequestUrl
          },
          LogLevel.dev
        );

        await fillFinalResponseWithAnotherSlots(nextRequestUrl);
      }
    };

    const startFetchTime = getCurrentPerformanceTime();
    await fillFinalResponseWithAnotherSlots(breakAdRequest);
    const endFetchTime = getCurrentPerformanceTime();

    logger(
      'ADO_BREAK_EXACT_LENGTH_RETURNED_SLOTS',
      { ids: this.getAdsPositionsArr(finalResponse), requestsCounter },
      LogLevel.dev
    );

    this.logAdServerRequestStats({
      startFetchTime,
      endFetchTime,
      requestedSlots: inputArgs.config.spots.length,
      version,
      connector: breakConnector,
      channel,
      isWithExactLength: inputArgs.config.exactLength,
      finalResponse,
      adUnitId: inputArgs.config.adUnitId
    });

    return finalResponse;
  }

  private getConfigLinearAdsIds(config: DaiAdsConfigADOBreak): (string | undefined)[] {
    return config.spots.map((c) => c.adId);
  }

  private getAdserverAdsIds(adserverAdsConfig: AdserverAd[]): (string | undefined)[] {
    return adserverAdsConfig.map((config) => this.getAdIdFromVast(config.vast));
  }

  private getAdIdFromVast(vast: AdVast4Normalized[]): string | undefined {
    const creative = vast?.[0].InLine?.Creatives.Creative;

    return creative?.[0]._attributes?.id;
  }

  private getAdsPositionsArr<T extends { position: number | null }>(arr: T[]) {
    return arr.map((s) => s.position);
  }

  private getAdoBreakAdsBody(vastData: Vast4Normalized): AdVast4Normalized[] {
    const adsElements = vastData.VAST?.Ad ?? [];

    return adsElements.filter((ad) => {
      const creative = ad.InLine?.Creatives?.Creative;
      return !!creative?.some((c) => !!c.UniversalAdId);
    });
  }
}

const generateADOBreakAdRequest = <
  IAdSubType extends {
    duration: number;
    position: number;
    adId?: string;
    atvType?: AtvTypeModel;
  }
>(paramsObj: {
  inputArgs: DaiAdsProviderOutputBreak;
  adSlots: IAdSubType[];
  slotRestrictions: string[];
  onlyPendingSlots: boolean;
}): string => {
  const { inputArgs, adSlots, slotRestrictions, onlyPendingSlots } = paramsObj;

  const { gdpr, gdprConsent } = inputArgs.request;
  const { channel, adUnitId, breakId } = inputArgs.config;

  const adIds: string[] = [];

  const positionParamsHelper = new URLParamsHelper('');

  (onlyPendingSlots
    ? adSlots.filter((slot) => !!slot.atvType?.pending?.length)
    : adSlots
  ).forEach((curr: IAdSubType) => {
    const { duration, position, adId, atvType } = curr;

    if (atvType?.pending?.includes(TransformType.dai) && !!adId) {
      adIds.push(adId);
    }

    const durationStr = duration.toString();
    positionParamsHelper
      .add(`mid${position}mindur`, durationStr)
      .add(`mid${position}dur`, durationStr)
      .add(`mid${position}maxdur`, durationStr);
  });

  const newBreakAdRequest = new RequestMacroParams(
    undefined,
    Protocol.http,
    undefined,
    undefined,
    undefined,
    gdpr,
    gdprConsent
  ).applyParams(validators.BASE_ADO_URL, inputArgs.request.headers);

  const urlHelper = new URLParamsHelper(newBreakAdRequest)
    .add('aocodetype', '1')
    .addMaybe('id', adUnitId)
    .add('ch', channel)
    .add('bid', breakId)
    .add('ct', 'linear')
    .add('gdpr', ParamsMacro.gdpr)
    .add('gdpr_consent', ParamsMacro.gdprConsent)
    .addMaybe('adid', adIds.join('_'))
    .addQueries(positionParamsHelper)
    .addMaybe(
      'tvn_restriction_labels',
      !slotRestrictions.length ? undefined : slotRestrictions.join(',')
    );

  return urlHelper.toString();
};
