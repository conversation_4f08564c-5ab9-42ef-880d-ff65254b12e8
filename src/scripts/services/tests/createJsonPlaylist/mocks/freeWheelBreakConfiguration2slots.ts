import {
  AdType,
  BreakConnector,
  Channel,
  IConfiguration,
  OrderType,
  TransformType
} from 'adpod-tools';

export const freeWheelBreakConfiguration2slots: IConfiguration = {
  id: '20250219MTIT000022302',
  time: '2025-02-20T00:38:46+01:00',
  duration: 255,
  channel: Channel.mtit,
  version: 'v1_0_0_dai_FW',
  adslot: [
    {
      position: 1,
      duration: 20,
      type: AdType.tv,
      vastmirroredadsJson: {
        VAST: {
          _attributes: {
            version: '4.0',
            'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
            xmlns: 'http://www.iab.com/VAST'
          },
          Ad: [
            {
              _attributes: {
                id: 'ITA-95882',
                sequence: 1
              },
              attributesToDrop: {
                campaignId: '',
                conditionalAd: false,
                breakId: '20250219MTIT000022302',
                linear: 'true'
              },
              InLine: {
                AdSystem: {
                  _text: 'Broadcast schedule IT',
                  _attributes: {
                    version: '4.0'
                  }
                },
                AdTitle: {
                  _text: 'Discovery Italy Linear Ad'
                },
                Creatives: {
                  Creative: [
                    {
                      _attributes: {
                        id: 'ITA-95882'
                      },
                      UniversalAdId: {
                        _attributes: {
                          idRegistry: 'DI-Ad-ID'
                        },
                        _text: 'ITA-95882'
                      },
                      Linear: {
                        Duration: {
                          _text: '00:00:20'
                        },
                        TrackingEvents: {
                          Tracking: [
                            {
                              _attributes: {
                                event: 'start'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D0%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'firstQuartile'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D25%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'midpoint'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D50%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'thirdQuartile'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D75%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'complete'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D100%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            }
                          ]
                        },
                        MediaFiles: {
                          MediaFile: [
                            {
                              _cdata:
                                'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-95882.mxf',
                              _attributes: {
                                delivery: 'progressive',
                                type: 'application/mxf',
                                width: 1920,
                                height: 1080
                              }
                            }
                          ],
                          Mezzanine: {
                            _cdata:
                              'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-95882.mxf',
                            _attributes: {
                              delivery: 'progressive',
                              type: 'application/mxf',
                              width: 1920,
                              height: 1080
                            }
                          }
                        }
                      }
                    }
                  ]
                },
                Impression: [
                  {
                    _attributes: {
                      id: ''
                    },
                    _cdata:
                      '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3Dimpression%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fm%3DIT'
                  }
                ],
                Error: [
                  {
                    _cdata:
                      '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D1%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-95882%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3Derror%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fm%3DIT'
                  }
                ],
                Extensions: {
                  Extension: [
                    {
                      _attributes: { type: 'wbdapm' },
                      SlotParameters: {
                        _cdata:
                          '{ "breakID": "20250219MTIT000022302", "breakType": "mirrored", "slotPosition": 1, "slotType": "mirrored" }'
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      },
      adrequest: '',
      metadata: {
        adId: 'ITA-95882',
        breakId: '20250219MTIT000022302',
        mezzanineUrl:
          'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-95882.mxf',
        orderType: OrderType.tv,
        atvType: {
          pending: [],
          processed: []
        }
      },
      connector: BreakConnector.none
    },
    {
      position: 2,
      duration: 20,
      type: AdType.atv,
      vastmirroredadsJson: {
        VAST: {
          _attributes: {
            version: '4.0',
            'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
            xmlns: 'http://www.iab.com/VAST'
          },
          Ad: [
            {
              _attributes: {
                id: 'ITA-91991',
                sequence: 2
              },
              attributesToDrop: {
                campaignId: '',
                conditionalAd: false,
                breakId: '20250219MTIT000022302',
                linear: 'true'
              },
              InLine: {
                AdSystem: {
                  _text: 'Broadcast schedule IT',
                  _attributes: {
                    version: '4.0'
                  }
                },
                AdTitle: {
                  _text: 'Discovery Italy Linear Ad'
                },
                Creatives: {
                  Creative: [
                    {
                      _attributes: {
                        id: 'ITA-91991'
                      },
                      UniversalAdId: {
                        _attributes: {
                          idRegistry: 'DI-Ad-ID'
                        },
                        _text: 'ITA-91991'
                      },
                      Linear: {
                        Duration: {
                          _text: '00:00:20'
                        },
                        TrackingEvents: {
                          Tracking: [
                            {
                              _attributes: {
                                event: 'start'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D0%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'firstQuartile'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D25%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'midpoint'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D50%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'thirdQuartile'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D75%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            },
                            {
                              _attributes: {
                                event: 'complete'
                              },
                              _cdata:
                                '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3D100%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fprefetch_cap%3D$PREFETCH_CAP%2Fm%3DIT'
                            }
                          ]
                        },
                        MediaFiles: {
                          MediaFile: [
                            {
                              _cdata:
                                'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-91991.mxf',
                              _attributes: {
                                delivery: 'progressive',
                                type: 'application/mxf',
                                width: 1920,
                                height: 1080
                              }
                            }
                          ],
                          Mezzanine: {
                            _cdata:
                              'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-91991.mxf',
                            _attributes: {
                              delivery: 'progressive',
                              type: 'application/mxf',
                              width: 1920,
                              height: 1080
                            }
                          }
                        }
                      }
                    }
                  ]
                },
                Impression: [
                  {
                    _attributes: {
                      id: ''
                    },
                    _cdata:
                      '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3Dimpression%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fm%3DIT'
                  }
                ],
                Error: [
                  {
                    _cdata:
                      '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl/?ed=p%3D2%2Fbid%3D20250219MTIT000022302%2Fch%3DMTIT%2Fuadid%3DITA-91991%2Fv%3Dv1_0_0_dai_FW%2Ft%3Dmirrored_no_dai%2Fuid%3D$UNIQUE_DEVICE_ID%2Fdur%3D20%2Fe%3Derror%2Fbt%3D$BREAK_TYPE%2Fcust_params%3D$CUST_PARAMS%2Fm%3DIT'
                  }
                ],
                Extensions: {
                  Extension: [
                    {
                      _attributes: {
                        type: 'wbdapm'
                      },
                      SlotParameters: {
                        _cdata:
                          '{ "breakID": "20250219MTIT000022302", "breakType": "mirrored", "slotPosition": 2, "slotType": "mirrored" }'
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      },
      adrequest:
        'https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY_MTIT&vprn=13813175&vrdu=10&vip=************&vdur=3600&resp=vmap1%2Bvast4&flag=+scpv+emcr+amcb+slcb+aeti;_fw_vcid2=1&testCampaign=true&v=v1_0_0_dai_FW&test=mxf&ch=MTIT&pod=20250219MTIT000022302&p=2&adid=ITA-91991&wbd_ad_dur=10&inventory=commercial_break&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F96.0.4664.110%20Safari%2F537.36;ptgt=a&mind=10&maxd=10&tpos=0&slau=Preroll%20Spot',
      metadata: {
        adId: 'ITA-91991',
        breakId: '20250219MTIT000022302',
        mezzanineUrl:
          'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-91991.mxf',
        orderType: OrderType.tv,
        atvType: {
          pending: [],
          processed: [TransformType.dai]
        }
      },
      connector: BreakConnector.freeWheelSchedule
    }
  ],
  metadata: {
    deapProfiles: true,
    trackingDaiAds: true,
    exactLenght: false,
    diffCreatives: false,
    additionalTrackingsFW: true,
    preroll: false,
    hasAtvSlot: true
  }
};
