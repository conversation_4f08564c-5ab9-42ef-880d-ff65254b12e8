import { Vast4Normalized, AdVast4Normalized } from 'adpod-tools';

type AdVast4NormalizedWithoutAttributesToDrop = Omit<AdVast4Normalized, 'attributesToDrop'>;
/*
 * This function drops attributes from every ad that are not needed in the final response. (attributesToDrop)
 */
export function dropUnnecessaryAttributesFromVast4(
  playlistVastJson: Vast4Normalized
): Vast4Normalized {
  playlistVastJson.VAST.Ad = dropUnnecessaryAttributesFromAd(playlistVastJson.VAST.Ad);

  return playlistVastJson;
}

export function dropUnnecessaryAttributesFromAd(
  ads: AdVast4Normalized[]
): AdVast4NormalizedWithoutAttributesToDrop[] {
  return ads.map(({ attributesToDrop, ...ad }) => ad);
}
