import { Injectable } from '@nestjs/common';
import { getActiveTraceId } from '../../logging/trace';
import { BreakConnector, Vast4Normalized } from 'adpod-tools';
import _ from 'lodash';
import { validators } from '../../../EnvValidation/envalidConfig';
import { ICacheProvider } from '../cache.provider';

const TTL = validators.DEBUG_CACHE_TTL;

export type Details = {
  breakId: string;
  adServerUrl: string;
  version: string;
  connector: BreakConnector;
};

export type BreakPositionInputData = {
  breakId: string;
  version: string;
  positions: number[];
};

export type BreakInputData = Details;

export type BreakDetails = Details[];

export type SlotInputData = Details & {
  position: number;
};

export type SlotDetails = SlotInputData;
export type FinalSlotsReplacedResult = {
  position?: number;
  version?: string;
  connector?: BreakConnector;
  breakId?: string;
};

export type DebugDetails = {
  slots: SlotDetails[][];
  break: BreakDetails;
  finalSlotsReplaced: FinalSlotsReplacedResult[];
};

type KeyTypes = '_slot' | '_break' | '_break_slots';

export abstract class IDebugService {
  abstract getDebugDetails(vasts: Vast4Normalized, breakId: string): Promise<string>;

  abstract setSpotDebugDetails(value: SlotInputData, ttl?: number): Promise<void>;

  abstract setBreakDebugDetails(data: BreakInputData, ttl?: number): Promise<void>;
}

@Injectable()
export class DebugService implements IDebugService {
  constructor(private readonly cache: ICacheProvider) {}

  public async getDebugDetails(vasts: Vast4Normalized, breakId: string): Promise<string> {
    const breakResults = await this.getBreakDetails();
    const slotResults = await this.getSlotDetails();
    const slotsReplaced = this.getSlotsReplacedDetails(vasts);

    const slotGroups = _.chain(slotResults)
      .groupBy(({ position }) => position)
      .map((value) => value.slice().reverse())
      .value();

    const debugDetails: DebugDetails = {
      finalSlotsReplaced: slotsReplaced,
      slots: slotGroups
        .map((slot) => slot.filter((s) => s.breakId === breakId))
        .filter((slot) => slot.length > 0),
      break: breakResults.filter((b) => b.breakId === breakId)
    };

    return JSON.stringify(debugDetails, null, 4);
  }

  public async setSpotDebugDetails(value: SlotInputData, ttl: number = TTL): Promise<void> {
    const { breakId, version, position } = value;

    const key = `debug_${getActiveTraceId()}_${version}_${breakId}_${position}_slot`;
    await this.cache.set(key, value, ttl);
  }

  public async setBreakDebugDetails(data: BreakInputData, ttl: number = TTL): Promise<void> {
    const { breakId, version } = data;

    const key = `debug_${getActiveTraceId()}_${version}_${breakId}_break`;
    const value = (await this.cache.get<BreakInputData[]>(key)) ?? [];

    if (value.length) {
      await this.cache.set(key, [...value, data], ttl);
    } else {
      await this.cache.set(key, [data], ttl);
    }
  }

  private async getKeys(endsWith: KeyTypes): Promise<string[]> {
    const keys = await this.cache.keys();
    return keys.filter(
      (key) => key.startsWith(`debug_${getActiveTraceId()}`) && key.endsWith(endsWith)
    );
  }

  private async getBreakDetails() {
    const breakKeys = await this.getKeys('_break');
    const breakResults = await this.cache.getMany<Details[]>(breakKeys);

    return breakResults.flat();
  }

  private async getSlotDetails() {
    const slotsKeys = await this.getKeys('_slot');
    const slotsResults = await this.cache.getMany<SlotInputData>(slotsKeys);

    return _.chain(slotsResults)
      .orderBy((x) => x.position)
      .value();
  }

  private getSlotsReplacedDetails(vasts: Vast4Normalized) {
    return vasts.VAST.Ad.filter(
      (adSlot) => adSlot.attributesToDrop?.connector && adSlot.attributesToDrop?.version
    ).map((adSlot) => ({
      position: adSlot._attributes.sequence,
      connector: adSlot.attributesToDrop?.connector,
      version: adSlot.attributesToDrop?.version,
      breakId: adSlot.attributesToDrop?.breakId
    }));
  }
}
